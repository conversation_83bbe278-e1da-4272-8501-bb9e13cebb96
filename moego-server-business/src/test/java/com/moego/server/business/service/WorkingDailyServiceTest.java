package com.moego.server.business.service;

import static com.moego.server.business.common.consts.DataSourceConst.READER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.moego.server.business.web.vo.StaffByWorkingDateVO;
import com.moego.server.business.web.vo.StaffWithWorkingRangeVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * WorkingDailyService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class WorkingDailyServiceTest {

    @InjectMocks
    private WorkingDailyService workingDailyService;

    @Mock
    private MigrateHelper migrateHelper;

    @Mock
    private StaffService staffService;

    @Mock
    private StaffWorkingHourService staffWorkingHourService;

    @Mock
    private MoeStaffMapper moeStaffMapper;

    /**
     * 测试 simpleQueryStaffWorkTime 方法的排序功能
     * 验证返回结果按照 sort 降序、staffId 升序排列
     */
    @Test
    void testSimpleQueryStaffWorkTime_SortOrder() {
        // 准备测试数据
        int businessId = 1;
        Integer tokenStaffId = 100;
        WorkingDailyQueryRangeVo rangeVo = new WorkingDailyQueryRangeVo()
                .setStartDate("2025-01-01")
                .setEndDate("2025-01-07")
                .setStaffIdList(List.of(1, 2, 3, 4));

        // Mock MigrateHelper
        MigrateInfo migrateInfo = new MigrateInfo(1L, true);
        when(migrateHelper.getMigrationInfo(businessId)).thenReturn(migrateInfo);

        // 创建测试用的员工数据，特意设置不同的 sort 值来测试排序
        Map<Integer, MoeStaffDto> staffMap = new HashMap<>();

        // 员工1: sort=10, staffId=1
        MoeStaffDto staff1 = MoeStaffDto.builder()
                .id(1)
                .firstName("Alice")
                .lastName("Smith")
                .sort(10)
                .build();
        staffMap.put(1, staff1);

        // 员工2: sort=20, staffId=2
        MoeStaffDto staff2 = MoeStaffDto.builder()
                .id(2)
                .firstName("Bob")
                .lastName("Johnson")
                .sort(20)
                .build();
        staffMap.put(2, staff2);

        // 员工3: sort=20, staffId=3 (相同sort，测试按staffId排序)
        MoeStaffDto staff3 = MoeStaffDto.builder()
                .id(3)
                .firstName("Charlie")
                .lastName("Brown")
                .sort(20)
                .build();
        staffMap.put(3, staff3);

        // 员工4: sort=30, staffId=4
        MoeStaffDto staff4 = MoeStaffDto.builder()
                .id(4)
                .firstName("David")
                .lastName("Wilson")
                .sort(30)
                .build();
        staffMap.put(4, staff4);

        // Mock StaffService
        when(staffService.getStaffMapByIds(1L, List.of(1, 2, 3, 4))).thenReturn(staffMap);

        // Mock queryStaffWorkTimeByRange 方法的返回值
        Map<Integer, Map<String, List<TimeRangeDto>>> workTimeMap = new HashMap<>();
        Map<String, List<TimeRangeDto>> timeRange = new HashMap<>();
        timeRange.put("2025-01-01", List.of(new TimeRangeDto(900, 1700))); // 9:00-17:00

        workTimeMap.put(1, timeRange);
        workTimeMap.put(2, timeRange);
        workTimeMap.put(3, timeRange);
        workTimeMap.put(4, timeRange);

        // 使用 spy 来 mock queryStaffWorkTimeByRange 方法
        WorkingDailyService spyService = spy(workingDailyService);
        doReturn(workTimeMap)
                .when(spyService)
                .queryStaffWorkTimeByRange(eq(businessId), anyList(), eq("2025-01-01"), eq("2025-01-07"));

        // 执行测试
        List<StaffWorkingRangeDto> result = spyService.simpleQueryStaffWorkTime(businessId, tokenStaffId, rangeVo);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证排序：按 sort 降序，然后按 staffId 升序
        // 期望顺序：staff4(sort=30,id=4) -> staff2(sort=20,id=2) -> staff3(sort=20,id=3) -> staff1(sort=10,id=1)
        assertEquals(4, result.get(0).getStaffId()); // sort=30, staffId=4
        assertEquals(30, result.get(0).getSort());

        assertEquals(2, result.get(1).getStaffId()); // sort=20, staffId=2 (较小的staffId)
        assertEquals(20, result.get(1).getSort());

        assertEquals(3, result.get(2).getStaffId()); // sort=20, staffId=3 (较大的staffId)
        assertEquals(20, result.get(2).getSort());

        assertEquals(1, result.get(3).getStaffId()); // sort=10, staffId=1
        assertEquals(10, result.get(3).getSort());

        // 验证其他字段也正确设置
        assertEquals("David", result.get(0).getFirstName());
        assertEquals("Wilson", result.get(0).getLastName());
        assertEquals("#FF0000", result.get(0).getColorCode());
        assertNotNull(result.get(0).getTimeRange());
    }

    /**
     * 测试相同 sort 值的员工按 staffId 升序排列
     */
    @Test
    void testSimpleQueryStaffWorkTime_SameSortDifferentStaffId() {
        // 准备测试数据
        int businessId = 1;
        Integer tokenStaffId = 100;
        var staffIdList = List.of(1, 2, 3);
        WorkingDailyQueryRangeVo rangeVo = new WorkingDailyQueryRangeVo()
                .setStartDate("2025-01-01")
                .setEndDate("2025-01-07")
                .setStaffIdList(staffIdList);

        // // 创建三个员工，都有相同的 sort 值
        // Map<Integer, MoeStaffDto> staffMap = new HashMap<>();
        //
        // MoeStaffDto staff1 = MoeStaffDto.builder()
        //         .id(1)
        //         .firstName("Alice")
        //         .lastName("Smith")
        //         .sort(15)
        //         .build();
        // MoeStaffDto staff3 = MoeStaffDto.builder()
        //         .id(3)
        //         .firstName("Charlie")
        //         .lastName("Brown")
        //         .sort(15)
        //         .build();
        // MoeStaffDto staff5 = MoeStaffDto.builder()
        //         .id(5)
        //         .firstName("Eve")
        //         .lastName("Davis")
        //         .sort(15)
        //         .build();
        //
        // staffMap.put(1, staff1);
        // staffMap.put(3, staff3);
        // staffMap.put(5, staff5);
        //
        // // Mock StaffService
        // when(staffService.getStaffMapByIds(1L, List.of(5, 1, 3))).thenReturn(staffMap);


        List<StaffByWorkingDateVO> staffResults = new ArrayList<>();
        var staffByWorkingDateVO1 = new StaffByWorkingDateVO();
        staffByWorkingDateVO1.setDate("2025-01-01");
        staffByWorkingDateVO1.setStaffs(List.of(
                new StaffWithWorkingRangeVO(1, List.of(new TimeRangeDto(540, 1020))),
                new StaffWithWorkingRangeVO(3, List.of(new TimeRangeDto(540, 1020))),
                new StaffWithWorkingRangeVO(5, List.of(new TimeRangeDto(540, 1020)))));
        staffResults.add(staffByWorkingDateVO1);
        when(staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(businessId, staffIdList,
                "2025-01-01", "2025-01-07")).thenReturn(staffResults);

        List<MoeStaff> staffList = new ArrayList<>();
        var moeStaff1 = new MoeStaff();
        moeStaff1.setId(1);
        moeStaff1.setSort(15);
        staffList.add(moeStaff1);
        var moeStaff3 = new MoeStaff();
        moeStaff3.setId(3);
        moeStaff3.setSort(15);
        staffList.add(moeStaff3);
        var moeStaff5 = new MoeStaff();
        moeStaff5.setId(5);
        moeStaff5.setSort(15);
        staffList.add(moeStaff5);
        when(moeStaffMapper.useDataSource(READER).queryStaffByIdListV2(staffIdList)).thenReturn(staffList);

        // 执行测试
        List<StaffWorkingRangeDto> result = workingDailyService.simpleQueryStaffWorkTime(businessId, tokenStaffId, rangeVo);

        // 验证结果：相同 sort 值时按 staffId 升序排列
        assertNotNull(result);
        assertEquals(3, result.size());

        // 期望顺序：staffId=1, staffId=3, staffId=5
        assertEquals(1, result.get(0).getStaffId());
        assertEquals(3, result.get(1).getStaffId());
        assertEquals(5, result.get(2).getStaffId());

        // 验证所有员工的 sort 值都相同
        assertEquals(15, result.get(0).getSort());
        assertEquals(15, result.get(1).getSort());
        assertEquals(15, result.get(2).getSort());
    }

    /**
     * 测试空员工列表的情况
     */
    @Test
    void testSimpleQueryStaffWorkTime_EmptyStaffMap() {
        // 准备测试数据
        int businessId = 1;
        Integer tokenStaffId = 100;
        var staffIdList = List.of(1, 2, 3);
        WorkingDailyQueryRangeVo rangeVo = new WorkingDailyQueryRangeVo()
                .setStartDate("2025-01-01")
                .setEndDate("2025-01-07")
                .setStaffIdList(staffIdList);

        List<StaffByWorkingDateVO> staffResults = new ArrayList<>();
        when(staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(businessId, staffIdList,
                        "2025-01-01", "2025-01-07")).thenReturn(staffResults);

        // 执行测试
        List<StaffWorkingRangeDto> result =
                workingDailyService.simpleQueryStaffWorkTime(businessId, tokenStaffId, rangeVo);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
